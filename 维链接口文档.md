# 维链接口文档

## 1.维链配置如下，从nacos中读取
	- third.weilian.url=https://api.uat.lupay.cn/sc-customer
	- third.weilian.clientId=0000019488056a9c059db33433a28000
	- third.weilian.clientSecret=gnpUuJIZ7mAQR6Tx/64uFMkMvklfnJNjPQRBtURSUAHmjDzUm0X/DQ==
	- third.weilian.customerId=404676578319171584

## 2、生成签名，后续的接口均需在header中使用，参数名为Authorization,值为“Bearer 签名”。签名计算规则为
	- 将除sign外的所有参数加上clientSecret，按参数名称以字母顺序排列；
	- 将排列后的参数按name=value进行拼接，以&分隔，得到签名原字符串；
	- 将原字符串md5得到签名

## 3、下单接口
 - 接口名: url+/order/sale/v2/submit
 - 请求方式: put
 - 参数:
    ```json
    {
    "customerOrderNo": "string", //取virtualOrder.orderSn
    "consigneePhone": "string", //取virtualOrder.rechargeAccount
    "saleSubGoodsSubmitVOS": [
        {
            "skuId": 0, //取virtualGoods.supplierItemId
            "quantity": 1
        }
    ],
    "notifyUrl": "string"
    }
   ```
 - 返回结果:
    ```json
   {
    "code": 0,
    "message": "",
    "data": {
    "saleId": 0,
    "customerOrderNo": ""
      }
    }
    ```
        

## 4、确认订单接口
 - 接口名: url+/order/sale/v2/confirm
   - 请求方式: put
   - 参数: saleId,取virtual_order表中的supplier_order_sn
   - 返回结果格式 
     ```json
        {
            "code": 0,
              "message": "",
              "data": {
              "saleId": 0,
              "customerOrderNo": ""
            }
        }
     ```

## 5、订单查询接口
 - 接口名: url+/order/sale/v2/get
 - 请求方式: get
 - 参数: customerOrderNo,取virtual_order表中的order_sn

   - 返回结果格式:
      ```json
         {
      "code": 0,
      "message": "",
      "data": {
      "saleId": 0,
      "customerOrderNo": "",
      "saleSubList": [
      {
      "subSaleId": 0,
      "goodsType": "",
      "consigneeName": "",
      "consigneePhone": "",
      "consigneeAccount": "",
      "freightTotal": 0.0,
      "amountTotal": 0.0,
      "state": "",
      "deliveryDate": "",
      "orderCompletedDate": "",
      "isSaleReturn": false,
      "consigneeCertificate": "",
      "ordererPhone": "",
      "deliveryAreaCode": "",
      "deliveryAddress": "",
      "players": "",
      "useDate": "",
      "wxAppId": "",
      "reOpenId": "",
      "sendName": "",
      "createdDate": "",
      "saleSubGoods": [
      {
      "spuId": 0,
      "skuId": 0,
      "goodsName": "",
      "specName": "",
      "priceUnit": 0.0,
      "goodsCount": 0,
      "amountSubtotal": 0.0,
      "freight": 0.0,
      "deliveryMethod": "",
      "codeList": [
      {
      "electronicCode": "",
      "verifierCode": "",
      "batchQrCodeImgUrl": "",
      "qrCode": "",
      "qrCodeImgUrl": "",
      "state": "",
      "sendCodeTime": "",
      "useTime": "",
      "invalidTime": "",
      "startTime": "",
      "overdueTime": "",
      "exchangeUrl": ""
      }
      ],
      "expressList": [
      {
      "waybillNo": "",
      "expressCompany": ""
      }
      ]
      }
      ]
      }
      ]
      }
      }
      ```  
## 维链回调
 - 若创建订单时notifyUrl参数填入链接地址，则标识该笔订单启用状态回推
 - 回推请求为 POST 形式
 - 订单仅在状态变为（发货、完成、取消）时，进行状态推送，其他状态不推送，如果需要查询其他状态的变化，请查询订单详情接口获取状态
- 订单状态说明：
枚举值:（可参考订单详情中的状态枚举，但回调时使用数字类型标识状态）
3 已发货
0 已完成
-1 已取消
 - 格式为
```json
    {
  "saleId": "382948381632294912",
  "saleSubId": "382948381636489216",
  "orderState": "3",
  "timestamp": "1732268520227",
  "sign": "b4b5b65b6bc02337cea03679a288fb97"
  }
   ```