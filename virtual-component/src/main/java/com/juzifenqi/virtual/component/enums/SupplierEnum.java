package com.juzifenqi.virtual.component.enums;

/**
 * 供应商枚举
 *
 * <AUTHOR>
 * @date 2023-07-14 10:33:23
 */
public enum SupplierEnum {

    /**
     * 1.子轩 3.橡树 4.福禄 5.荔枝 6.维链
     */
    SUPPLIER_ZX(1, "子轩", "zixuanUtil"), SUPPLIER_XS(3, "橡树", "xiangshuBusHandler"), SUPPLIER_FULU(4, "福禄", "fuluBusHandler")
    ,SUPPLIER_LIZHI(5, "荔枝", "lizhiHandler"), SUPPLIER_WEILIAN(6, "维链", "weilianUtil");

    private int    code;
    private String name;
    private String beanName;

    SupplierEnum(int code, String name, String beanName) {
        this.code = code;
        this.name = name;
        this.beanName = beanName;
    }


    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getBeanName() {
        return beanName;
    }

    public void setBeanName(String beanName) {
        this.beanName = beanName;
    }

    /**
     * 通过code 获取 BeanName
     *
     * @param code 供应商id
     */
    public static String getBeanNameByCode(int code) {
        for (SupplierEnum supplierEnum : SupplierEnum.values()) {
            if (code == supplierEnum.getCode()) {
                return supplierEnum.getBeanName();
            }
        }
        return null;
    }

}
