package com.juzifenqi.virtual.core.services.impl.third.weilian;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.groot.utils.http.OKHttp3SimpleUtils;
import com.juzifenqi.virtual.bean.pojo.VirtualOrders;
import com.juzifenqi.virtual.bean.pojo.VirtualOrdersCallback;
import com.juzifenqi.virtual.bean.pojo.VirtualOrdersDoubt;
import com.juzifenqi.virtual.bean.pojo.VirtualOrdersWorker;
import com.juzifenqi.virtual.bean.pojo.third.weilian.request.WlRechargeStatusNotice;
import com.juzifenqi.virtual.bean.vo.RechargeRespVo;
import com.juzifenqi.virtual.bean.vo.VirtualOrdersWorkerVo;
import com.juzifenqi.virtual.component.enums.OrderRechargeStatusEnum;
import com.juzifenqi.virtual.component.enums.OrderStatusEnum;
import com.juzifenqi.virtual.component.enums.RechargeStatusEnum;
import com.juzifenqi.virtual.component.models.VirtualOrdersTrailModel;
import com.juzifenqi.virtual.component.util.RobotUtil;
import com.juzifenqi.virtual.core.services.impl.third.AbstractStrategyHandler;
import com.juzifenqi.virtual.core.services.impl.third.common.CommonBusHandler;
import com.juzifenqi.virtual.core.services.impl.third.common.MqResultCodeEnum;
import com.juzifenqi.virtual.dao.VirtualOrdersCallbackDao;
import com.juzifenqi.virtual.dao.VirtualOrdersDao;
import com.juzifenqi.virtual.dao.VirtualOrdersDoubtDao;
import com.juzifenqi.virtual.dao.VirtualOrdersWorkerDao;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.security.MessageDigest;
import java.util.*;

/**
 * 维链三方交互工具类
 *
 * <AUTHOR>
 * @date 2025/08/23
 */
@Component
@Slf4j
public class WeilianHandler extends AbstractStrategyHandler {

    @Autowired
    private VirtualOrdersWorkerDao virtualOrdersWorkerDao;
    @Autowired
    private VirtualOrdersDao virtualOrdersDao;
    @Autowired
    private VirtualOrdersDoubtDao virtualOrdersDoubtDao;
    @Autowired
    private VirtualOrdersCallbackDao virtualOrdersCallbackDao;
    @Autowired
    private WeilianConfig config;
    @Autowired
    private RobotUtil robotUtil;
    @Autowired
    private VirtualOrdersTrailModel ordersTrailModel;
    @Autowired
    private CommonBusHandler commonBusHandler;

    // 维链状态码常量
    // 成功
    private static final String WL_SUCCESS_CODE = "0";
    // 已完成
    private static final String WL_ORDER_COMPLETED = "0";
    // 已发货
    private static final String WL_ORDER_SHIPPED = "3";
    // 已取消
    private static final String WL_ORDER_CANCELLED = "-1";

    // 最大查证次数
    public static final Integer MAX_RETRY_QUERY_TIMES = 10;
    // 维链要求的回调成功响应
    public static final String CALLBACK_RSP_SUCCESS = "success";
    // 失败响应
    public static final String CALLBACK_RSP_FAIL = "fail";

    /**
     * 生成维链签名
     * 将除sign外的所有参数加上clientSecret，按参数名称以字母顺序排列
     * 将排列后的参数按name=value进行拼接，以&分隔，得到签名原字符串
     * 将原字符串md5得到签名
     */
    private String generateSign(Map<String, Object> params) {
        // 添加clientSecret用于签名
        params.put("clientSecret", config.getClientSecret());
        
        List<String> keys = new ArrayList<>(params.keySet());
        Collections.sort(keys);

        StringBuilder signStr = new StringBuilder();
        for (int i = 0; i < keys.size(); i++) {
            String key = keys.get(i);
            Object value = params.get(key);
            if (value != null) {
                if (i > 0) {
                    signStr.append("&");
                }
                signStr.append(key).append("=").append(value);
            }
        }

        log.info("维链签名原字符串: {}", signStr.toString());
        String sign = md5(signStr.toString());
        
        // 移除clientSecret，不传输
        params.remove("clientSecret");
        
        return sign;
    }

    /**
     * MD5加密
     */
    private String md5(String str) {
        if (str == null) {
            return null;
        }
        try {
            MessageDigest messageDigest = MessageDigest.getInstance("MD5");
            messageDigest.update(str.getBytes());
            return bytesToHexString(messageDigest.digest());
        } catch (Exception e) {
            throw new RuntimeException("MD5加密失败", e);
        }
    }

    /**
     * 将二进制转换成16进制
     */
    private String bytesToHexString(byte[] src) {
        StringBuilder stringBuilder = new StringBuilder();
        if (src == null || src.length <= 0) {
            return null;
        }
        for (int i = 0; i < src.length; i++) {
            int v = src[i] & 0xFF;
            String hv = Integer.toHexString(v);
            if (hv.length() < 2) {
                stringBuilder.append(0);
            }
            stringBuilder.append(hv);
        }
        return stringBuilder.toString();
    }

    /**
     * 充值结果查证
     */
    @Override
    public RechargeRespVo getRechargeStatus(VirtualOrdersWorkerVo paramVo) {
        log.info("维链-充值结果查证：param:{}", JSON.toJSONString(paramVo));
        long start = System.currentTimeMillis();
        VirtualOrders order = paramVo.getVirtualOrder();

        // 构建查询参数
        Map<String, Object> params = new HashMap<>();
        params.put("customerOrderNo", order.getOrderSn());

        // 生成签名
        String sign = generateSign(params);

        log.info("维链充值状态查证开始,订单:{}", order.getOrderSn());

        try {
            String methodUrl = config.getUrl() + "/order/sale/v2/get";

            log.info("======调取维链接口：订单查询接口===开始, url={}, param={}, sign={}", methodUrl, JSONObject.toJSONString(params), sign);

            // 构建完整的GET请求URL，包含签名参数
            String getUrl = methodUrl + "?customerOrderNo=" + order.getOrderSn() + "&sign=" + sign;

            // 使用GET请求，暂时不使用Authorization header，将签名作为参数传递
            Map<String, Object> emptyParams = new HashMap<>();
            JSONObject resultJson = OKHttp3SimpleUtils.postByForm(getUrl, emptyParams);
            
            log.info("======调取维链接口：订单查询接口===返回结果,result={}", resultJson.toJSONString());

            if (WL_SUCCESS_CODE.equals(resultJson.getString("code"))) {
                JSONObject data = resultJson.getJSONObject("data");
                if (data != null && data.getJSONArray("saleSubList") != null && !data.getJSONArray("saleSubList").isEmpty()) {
                    JSONObject saleSubInfo = data.getJSONArray("saleSubList").getJSONObject(0);
                    String state = saleSubInfo.getString("state");
                    
                    return handleQueryResult(paramVo, order, state);
                } else {
                    return handleQueryUnknown(paramVo, order, "订单数据为空");
                }
            } else {
                return handleQueryUnknown(paramVo, order, resultJson.getString("message"));
            }
        } catch (Exception e) {
            log.error("维链充值状态查证发生异常,订单:{},详情:{}", order.getOrderSn(), e.getMessage(), e);
            return handleQueryUnknown(paramVo, order, "查询接口异常: " + e.getMessage());
        } finally {
            long end = System.currentTimeMillis();
            log.info("=====>维链查证处理一笔订单耗时: {} ms", end - start);
        }
    }

    /**
     * 处理查询结果
     */
    private RechargeRespVo handleQueryResult(VirtualOrdersWorkerVo paramVo, VirtualOrders order, String state) {
        if (WL_ORDER_SHIPPED.equals(state)) {
            // 充值成功处理
            return handleRechargeSuccess(paramVo, order, "维链主动查证充值成功");
        } else if (WL_ORDER_CANCELLED.equals(state)) {
            // 充值失败处理
            return handleRechargeFail(paramVo, order, "维链主动查证充值失败: ");
        } else {
            // 其他状态继续等待
            return handleQueryUnknown(paramVo, order, "订单状态: " + state);
        }
    }

    /**
     * 处理充值成功
     */
    private RechargeRespVo handleRechargeSuccess(VirtualOrdersWorkerVo paramVo, VirtualOrders order, String remark) {
        // 更新子表订单状态
        VirtualOrdersWorker worker = new VirtualOrdersWorker();
        worker.setId(paramVo.getId());
        worker.setOrderRechargeStatus(OrderRechargeStatusEnum.RECHARGE_SUCCESS.getCode());
        worker.setOrderRechargeTime(new Date());
        worker.setRemark(remark);
        virtualOrdersWorkerDao.updateVirtualOrdersWorkerById(worker);

        // 更新主表订单状态
        VirtualOrders virtualOrders = new VirtualOrders();
        virtualOrders.setId(order.getId());
        virtualOrders.setOrderStatus(OrderStatusEnum.STATUS_RECHARGE_SUCCESS.getCode());
        virtualOrders.setRemark(OrderStatusEnum.STATUS_RECHARGE_SUCCESS.getDesc());
        virtualOrders.setRechargeStatus(RechargeStatusEnum.RECHARGE_SUCCESS.getCode());
        virtualOrdersDao.updateVirtualOrdersById(virtualOrders);

        // 记录订单流转状态
        ordersTrailModel.saveOrdersTrail(order.getOrderSn(),
                OrderStatusEnum.STATUS_ORDER_SUCCESS.getCode(),
                OrderStatusEnum.STATUS_RECHARGE_SUCCESS.getCode(), remark);

        log.info("维链主动查证充值成功,订单:{}", order.getOrderSn());
        
        // 推送充值成功MQ
        commonBusHandler.sendRechargeResultMq(order, MqResultCodeEnum.SUCCESS);
        
        return null;
    }

    /**
     * 处理充值失败
     */
    private RechargeRespVo handleRechargeFail(VirtualOrdersWorkerVo paramVo, VirtualOrders order, String remark) {
        // 更新子表订单状态
        VirtualOrdersWorker worker = new VirtualOrdersWorker();
        worker.setId(paramVo.getId());
        worker.setOrderRechargeStatus(OrderRechargeStatusEnum.RECHARGE_FAIL.getCode());
        worker.setOrderRechargeTime(new Date());
        worker.setRemark(remark);
        virtualOrdersWorkerDao.updateVirtualOrdersWorkerById(worker);

        // 更新主表订单状态
        VirtualOrders virtualOrders = new VirtualOrders();
        virtualOrders.setId(order.getId());
        virtualOrders.setOrderStatus(OrderStatusEnum.STATUS_RECHARGE_FAIL.getCode());
        virtualOrders.setRemark(OrderStatusEnum.STATUS_RECHARGE_FAIL.getDesc());
        virtualOrders.setRechargeStatus(RechargeStatusEnum.RECHARGE_FAIL.getCode());
        virtualOrdersDao.updateVirtualOrdersById(virtualOrders);

        // 是否0元订单
        boolean zeroOrder = order.getOrderMoney().compareTo(BigDecimal.ZERO) == 0;
        String trailRemark = zeroOrder ? "主动查证充值失败,0元订单不退款" : "主动查证充值失败";
        
        // 记录订单流转状态
        ordersTrailModel.saveOrdersTrail(order.getOrderSn(),
                OrderStatusEnum.STATUS_ORDER_SUCCESS.getCode(),
                OrderStatusEnum.STATUS_RECHARGE_FAIL.getCode(), trailRemark);

        log.info("维链主动查证充值失败,订单:{}", order.getOrderSn());

        // 0元订单不调用订单中心接口
        if (!zeroOrder) {
            commonBusHandler.cancelOrder(order, "维链主动查证充值失败");
        }
        
        // 推送充值失败MQ
        commonBusHandler.sendRechargeResultMq(order, MqResultCodeEnum.FAIL);
        
        return null;
    }

    /**
     * 处理查询结果未知
     */
    private RechargeRespVo handleQueryUnknown(VirtualOrdersWorkerVo paramVo, VirtualOrders order, String message) {
        if (paramVo.getGetRetryCount() + 1 == MAX_RETRY_QUERY_TIMES) {
            // 存疑处理
            return handleRechargeDoubt(paramVo, order, message);
        } else {
            // 增加重试次数，等待下次调度
            VirtualOrdersWorker worker = new VirtualOrdersWorker();
            worker.setId(paramVo.getId());
            worker.setGetRetryCount(paramVo.getGetRetryCount() + 1);
            worker.setRemark("维链充值还未有明确结果,等待下次调度重试: " + message);
            virtualOrdersWorkerDao.updateVirtualOrdersWorkerById(worker);

            log.info("维链充值还未有明确结果,订单:{},第{}次查证重试", order.getOrderSn(), worker.getGetRetryCount());
            return null;
        }
    }

    /**
     * 处理充值存疑
     */
    private RechargeRespVo handleRechargeDoubt(VirtualOrdersWorkerVo paramVo, VirtualOrders order, String message) {
        // 更新子表订单状态
        VirtualOrdersWorker worker = new VirtualOrdersWorker();
        worker.setId(paramVo.getId());
        worker.setOrderRechargeStatus(OrderRechargeStatusEnum.RECHARGE_FAIL.getCode());
        worker.setOrderRechargeTime(new Date());
        worker.setGetRetryCount(MAX_RETRY_QUERY_TIMES);
        worker.setRemark("维链充值结果存疑: " + message);
        virtualOrdersWorkerDao.updateVirtualOrdersWorkerById(worker);

        // 更新主表订单状态
        VirtualOrders virtualOrders = new VirtualOrders();
        virtualOrders.setId(order.getId());
        virtualOrders.setOrderStatus(OrderStatusEnum.STATUS_RECHARGE_FAIL.getCode());
        virtualOrders.setRemark(OrderStatusEnum.STATUS_RECHARGE_FAIL.getDesc());
        virtualOrders.setRechargeStatus(RechargeStatusEnum.RECHARGE_FAIL.getCode());
        virtualOrdersDao.updateVirtualOrdersById(virtualOrders);

        // 是否0元订单
        boolean zeroOrder = order.getOrderMoney().compareTo(BigDecimal.ZERO) == 0;
        String remark = zeroOrder ? "充值结果存疑,0元订单不退款" : "充值结果存疑";

        // 记录订单流转状态
        ordersTrailModel.saveOrdersTrail(order.getOrderSn(),
                OrderStatusEnum.STATUS_ORDER_SUCCESS.getCode(),
                OrderStatusEnum.STATUS_RECHARGE_UNKNOWN.getCode(), remark);

        // 保存存疑订单
        VirtualOrdersDoubt virtualOrdersDoubt = new VirtualOrdersDoubt();
        virtualOrdersDoubt.setUserId(order.getUserId());
        virtualOrdersDoubt.setChannelId(order.getChannelId());
        virtualOrdersDoubt.setOrderSn(order.getOrderSn());
        virtualOrdersDoubt.setReason(message);
        virtualOrdersDoubt.setCreateTime(new Date());
        virtualOrdersDoubtDao.saveVirtualOrdersDoubt(virtualOrdersDoubt);

        // 推送充值失败MQ
        commonBusHandler.sendRechargeResultMq(order, MqResultCodeEnum.FAIL);

        // 存疑订单自动退款，0元订单不调用订单中心接口
        if (!zeroOrder) {
            commonBusHandler.cancelOrder(order, "维链主动查证充值存疑");
        }

        log.info("维链充值结果存疑,订单:{},详情:{}", order.getOrderSn(), message);

        // 钉钉告警提示存疑订单
        String msg = "维链充值结果存疑,订单:" + order.getOrderSn() + ",详情:" + message;
        robotUtil.pushMsg(msg);

        return null;
    }

    /**
     * 回调处理
     */
    public String rechargeStatusNotice(WlRechargeStatusNotice statusNotice) {
        log.info("维链回调通知订单结果开始:{}", JSON.toJSONString(statusNotice));

        // 参数校验
        if (StringUtils.isBlank(statusNotice.getSaleId()) ||
            StringUtils.isBlank(statusNotice.getOrderState()) ||
            StringUtils.isBlank(statusNotice.getTimestamp()) ||
            StringUtils.isBlank(statusNotice.getSign())) {
            log.info("维链回调通知订单结果失败-请求参数不合法,必填字段为空");
            return CALLBACK_RSP_FAIL;
        }

        // 签名验证
        if (!verifyCallbackSign(statusNotice)) {
            log.info("维链回调通知订单结果失败-签名验证失败");
            return CALLBACK_RSP_FAIL;
        }

        // 根据saleId查找订单（维链的saleId对应我们的supplierOrderSn）
        VirtualOrdersWorker virtualOrdersWorker = virtualOrdersWorkerDao.getVirtualOrdersWorkerBySupplierOrderSn(statusNotice.getSaleId());
        if (virtualOrdersWorker == null) {
            log.info("维链回调通知订单结果失败-订单不存在,传参维链订单号:{}", statusNotice.getSaleId());
            return CALLBACK_RSP_FAIL;
        }

        // 主动查证已经有结果了
        if (virtualOrdersWorker.getOrderRechargeStatus() != 0) {
            log.info("维链主动查证已经有结果了,不处理回调,订单号:{}", virtualOrdersWorker.getOrderSn());
            orderCallback(statusNotice, CALLBACK_RSP_SUCCESS, "主动查证已经有结果");
            return CALLBACK_RSP_SUCCESS;
        }

        String dealStatus = dealStatusNotice(statusNotice, virtualOrdersWorker.getId());
        orderCallback(statusNotice, dealStatus, null);
        return dealStatus;
    }

    /**
     * 验证回调签名
     */
    private boolean verifyCallbackSign(WlRechargeStatusNotice statusNotice) {
        try {
            Map<String, Object> params = new HashMap<>();
            params.put("saleId", statusNotice.getSaleId());
            params.put("saleSubId", statusNotice.getSaleSubId());
            params.put("orderState", statusNotice.getOrderState());
            params.put("timestamp", statusNotice.getTimestamp());

            String expectedSign = generateSign(params);
            return expectedSign.equals(statusNotice.getSign());
        } catch (Exception e) {
            log.error("维链回调签名验证异常", e);
            return false;
        }
    }

    /**
     * 处理状态通知
     */
    private String dealStatusNotice(WlRechargeStatusNotice statusNotice, Integer workerId) {
        boolean success = WL_ORDER_SHIPPED.equals(statusNotice.getOrderState());
        boolean cancelled = WL_ORDER_CANCELLED.equals(statusNotice.getOrderState());

        if (!success && !cancelled) {
            // 其他状态（如已发货）暂不处理
            log.info("维链回调状态为:{},暂不处理", statusNotice.getOrderState());
            return CALLBACK_RSP_SUCCESS;
        }

        VirtualOrders order = virtualOrdersDao.getVirtualOrdersBySupplierOrderSn(statusNotice.getSaleId());
        if (order == null) {
            log.error("维链回调处理失败-根据supplierOrderSn未找到订单:{}", statusNotice.getSaleId());
            return CALLBACK_RSP_FAIL;
        }

        // 更新任务表订单状态
        VirtualOrdersWorker worker = new VirtualOrdersWorker();
        worker.setId(workerId);
        worker.setOrderRechargeStatus(success ? OrderRechargeStatusEnum.RECHARGE_SUCCESS.getCode()
                : OrderRechargeStatusEnum.RECHARGE_FAIL.getCode());
        worker.setOrderRechargeTime(new Date());
        worker.setRemark(success ? "维链回调通知充值成功" : "维链回调通知充值失败");
        virtualOrdersWorkerDao.updateVirtualOrdersWorkerById(worker);

        // 更新主表订单状态
        VirtualOrders virtualOrders = new VirtualOrders();
        virtualOrders.setId(order.getId());
        virtualOrders.setOrderStatus(success ? OrderStatusEnum.STATUS_RECHARGE_SUCCESS.getCode()
                : OrderStatusEnum.STATUS_RECHARGE_FAIL.getCode());
        virtualOrders.setRemark(success ? OrderStatusEnum.STATUS_RECHARGE_SUCCESS.getDesc()
                : OrderStatusEnum.STATUS_RECHARGE_FAIL.getDesc());
        virtualOrders.setRechargeStatus(success ? RechargeStatusEnum.RECHARGE_SUCCESS.getCode()
                : RechargeStatusEnum.RECHARGE_FAIL.getCode());
        virtualOrdersDao.updateVirtualOrdersById(virtualOrders);

        // 记录订单流转状态
        ordersTrailModel.saveOrdersTrail(order.getOrderSn(),
                OrderStatusEnum.STATUS_ORDER_SUCCESS.getCode(),
                success ? OrderStatusEnum.STATUS_RECHARGE_SUCCESS.getCode()
                        : OrderStatusEnum.STATUS_RECHARGE_FAIL.getCode(),
                success ? "维链回调通知充值成功" : "维链回调通知充值失败");

        log.info("处理维链回调通知 {} ,订单:{}", success ? "充值成功" : "充值失败", order.getOrderSn());

        if (!success) {
            // 充值失败用户订单退款
            boolean zeroOrder = order.getOrderMoney().compareTo(BigDecimal.ZERO) == 0;
            // 0元订单不调用订单中心接口
            if (!zeroOrder) {
                commonBusHandler.cancelOrder(order, "维链回调通知充值失败");
            }
        }

        // 发送维链充值结果MQ
        commonBusHandler.sendRechargeResultMq(order, success ? MqResultCodeEnum.SUCCESS : MqResultCodeEnum.FAIL);

        return CALLBACK_RSP_SUCCESS;
    }

    /**
     * 记录维链回调状态
     */
    private void orderCallback(WlRechargeStatusNotice statusNotice, String response, String remark) {
        // 记录回调结果
        VirtualOrdersCallback virtualOrdersCallback = new VirtualOrdersCallback();
        virtualOrdersCallback.setOrderSn(findOrderSnBySaleId(statusNotice.getSaleId()));
        virtualOrdersCallback.setSupplierOrderSn(statusNotice.getSaleId());
        virtualOrdersCallback.setCallbackOrderStatus(statusNotice.getOrderState());
        virtualOrdersCallback.setCallbackOrderDesc("维链回调状态:" + statusNotice.getOrderState());
        virtualOrdersCallback.setResponse(response);
        virtualOrdersCallback.setRemark(remark);
        virtualOrdersCallbackDao.saveVirtualOrdersCallback(virtualOrdersCallback);
    }

    /**
     * 根据saleId查找订单号
     */
    private String findOrderSnBySaleId(String saleId) {
        try {
            VirtualOrders order = virtualOrdersDao.getVirtualOrdersBySupplierOrderSn(saleId);
            return order != null ? order.getOrderSn() : saleId;
        } catch (Exception e) {
            log.error("根据saleId查找订单号异常", e);
            return saleId;
        }
    }
}
