package com.juzifenqi.virtual.core.services.impl.third.weilian;

import com.alibaba.nacos.api.config.annotation.NacosValue;
import lombok.Data;
import org.springframework.stereotype.Component;

/**
 * 维链三方交互配置类
 *
 * <AUTHOR>
 * @date 2025/08/23
 */
@Data
@Component
public class WeilianConfig {

    /**
     * 维链接口地址
     */
    @NacosValue(value = "${third.weilian.url}", autoRefreshed = true)
    private String url;

    /**
     * 客户端ID
     */
    @NacosValue(value = "${third.weilian.clientId}", autoRefreshed = true)
    private String clientId;

    /**
     * 客户端密钥（仅用于签名，不传输）
     */
    @NacosValue(value = "${third.weilian.clientSecret}", autoRefreshed = true)
    private String clientSecret;

    /**
     * 客户ID
     */
    @NacosValue(value = "${third.weilian.customerId}", autoRefreshed = true)
    private String customerId;

    /**
     * 订单状态回调地址
     */
    @NacosValue(value = "${third.weilian.callbackUrl}", autoRefreshed = true)
    private String callbackUrl;
}
