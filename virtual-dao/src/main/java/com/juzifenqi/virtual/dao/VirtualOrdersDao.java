package com.juzifenqi.virtual.dao;

import com.juzifenqi.virtual.bean.bo.VirtualOrdersBo;
import com.juzifenqi.virtual.bean.pojo.VirtualOrders;
import java.util.List;
import org.apache.ibatis.annotations.Param;


public interface VirtualOrdersDao {

    /**
     * 根据权益订单号查询订单
     */
    VirtualOrders getVirtualOrdersByOrderSn(@Param("orderSn") String orderSn);

    /**
     * 根据供应商订单号查询订单
     */
    VirtualOrders getVirtualOrdersBySupplierOrderSn(@Param("supplierOrderSn") String supplierOrderSn);

    /**
     * 根据主键更新记录
     */
    Integer updateVirtualOrdersById(VirtualOrders virtualOrders);


    /**
     * 根据会员订单号统计有效权益结算订单
     */
    Integer countUesdOrdersByOrderSn(@Param("orderSn") String orderSn,
            @Param("modelId") Integer modelId);

    /**
     * 获取期限内，通过该会员单号下的订单
     */
    List<VirtualOrders> countOrderByPlus(String plusOrderSn, Integer modelId);

    /**
     * 是否有充值中订单（待充值下单，下单成功，待充值查证，充值成功，存疑订单）
     */
    Integer countProcessingOrder(String plusOrderSn, Integer modelId);

    /**
     * 充值中订单列表（待充值下单，下单成功，待充值查证，充值成功，存疑订单）
     */
    List<VirtualOrders> selectProcessingOrder(String plusOrderSn, Integer modelId);

    /**
     * 获取商城下单未支付订单
     */
    List<VirtualOrders> getNeedPayOrder(String plusOrderSn, String productSku, Integer modelId);


    /**
     * 权益订单保存
     */
    Integer saveVirtualOrders(VirtualOrders virtualOrders);


    /**
     * 根据权益订单号orderSn更新权益订单状态orderStatus
     */
    Integer updateOrderStatusByOrderSn(@Param("orderSn") String orderSn,
            @Param("orderStatus") int orderStatus, @Param("remark") String remark);


    /**
     * 多条件查询结算订单列表(分页)
     */
    List<VirtualOrders> getVirtualOrdersListByPage(
            @Param("virtualOrdersBo") VirtualOrdersBo virtualOrdersBo,
            @Param("start") Integer start, @Param("size") Integer size);

    /**
     * 查询已完成结算订单列表信息
     */
    List<VirtualOrders> getVirtualOrdersList(VirtualOrdersBo virtualOrdersBo);

    int pageListCount(@Param("virtualOrdersBo") VirtualOrdersBo virtualGoodsBo);

    /**
     * 获取用户虚拟订单分页列表
     */
    List<VirtualOrders> getOrdersListByUser(VirtualOrdersBo virtualOrdersBo);

    /**
     * 用户虚拟订单列表总数
     */
    Integer countOrdersListByUser(VirtualOrdersBo virtualOrdersBo);

    /**
     * 按虚拟订单号修改
     */
    Integer updateOrderByOrderSn(VirtualOrders orders);

    /**
     * 获取信息
     */
    VirtualOrders getByOrderSn(String orderSn);

    /**
     * 获取商城下单未支付订单
     */
    List<VirtualOrders> getRechargeSuccessOrders(@Param("plusOrderSn") String plusOrderSn,
            @Param("rechargeStatus") Integer rechargeStatus, @Param("payStatus") Integer payStatus,
            @Param("supplierIds") List<Integer> supplierIds, @Param("rechargeTypes") List<Integer> rechargeTypes);

    /**
     * 查询全部订单信息
     */
    List<VirtualOrders> getAllVirtualOrdersList(VirtualOrdersBo virtualOrdersBo);

    /**
     * 批量查询手机号密文为空的订单
     */
    List<VirtualOrders> getWithEmptyUuidVirtualOrders(@Param("index") Long index, @Param("batchSize") Integer batchSize);

    /**
     * 批量查询手机号密文不为空的订单
     */
    List<VirtualOrders> getWithUuidVirtualOrders(@Param("index") Long index, @Param("batchSize") Integer batchSize);

    /**
     * 根据id获取订单信息
     */
    VirtualOrders getVirtualOrdersById(@Param("id") Integer id);

    /**
     * 批量更新
     */
    Integer updateBatchById(@Param("list") List<VirtualOrders> updateList);

}
