package com.juzifenqi.virtual.web.api.third;

import com.juzifenqi.virtual.bean.pojo.third.weilian.request.WlRechargeStatusNotice;
import com.juzifenqi.virtual.core.services.impl.third.weilian.WeilianHandler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 维链回调
 *
 * <AUTHOR>
 * @date 2025/08/23
 */
@RestController
@RequestMapping("/weilian")
public class WlController {

    @Autowired
    private WeilianHandler weilianHandler;

    @PostMapping("/rechargeStatusNotice")
    public String rechargeStatusNotice(@RequestBody WlRechargeStatusNotice wlRechargeStatusNotice) {
        return weilianHandler.rechargeStatusNotice(wlRechargeStatusNotice);
    }
}
